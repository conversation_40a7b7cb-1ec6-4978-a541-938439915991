/* Custom animations for AuthorPitch component - Matching Pitch.css exactly */

/* Pitch Container */
.pitch-container {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  animation: fadeIn 0.6s ease-out;
}

.pitch-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(59,130,246,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(59,130,246,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(59,130,246,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInList {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 60px;
  }
}

/* Apply animations */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slideInRight {
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slideInUp {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInList {
  animation: fadeInList 0.8s ease-out;
}

.animate-expandWidth {
  animation: expandWidth 1s ease-out 0.5s both;
}

/* Staggered animation delays for cards */
.animate-slideInUp:nth-child(1) { animation-delay: 0s; }
.animate-slideInUp:nth-child(2) { animation-delay: 0.1s; }
.animate-slideInUp:nth-child(3) { animation-delay: 0.2s; }
.animate-slideInUp:nth-child(4) { animation-delay: 0.3s; }
.animate-slideInUp:nth-child(5) { animation-delay: 0.4s; }
.animate-slideInUp:nth-child(6) { animation-delay: 0.5s; }

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Enhanced focus states */
input:focus,
select:focus,
textarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions for all interactive elements */
button,
input,
select,
textarea {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for cards */
.pitch-card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
}

/* Gradient text utilities */
.gradient-text-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button shimmer effect - Matching original Pitch component */
.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.shimmer-effect:hover::before {
  left: 100%;
}

/* Create New Button - Exact match to original */
.create-new-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.create-new-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .animate-slideInDown,
  .animate-slideInLeft,
  .animate-slideInRight,
  .animate-slideInUp {
    animation-duration: 0.4s;
  }
  
  .pitch-card-hover:hover {
    transform: translateY(-4px);
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: rgba(71, 85, 105, 0.1);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced backdrop blur for better browser support */
.backdrop-blur-fallback {
  background-color: rgba(255, 255, 255, 0.95);
}

@supports (backdrop-filter: blur(20px)) {
  .backdrop-blur-fallback {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
  }
}

/* Print styles */
@media print {
  .animate-fadeIn,
  .animate-slideInDown,
  .animate-slideInLeft,
  .animate-slideInRight,
  .animate-slideInUp,
  .animate-fadeInList {
    animation: none;
  }
  
  .pitch-card-hover:hover {
    transform: none;
    box-shadow: none;
  }
}
